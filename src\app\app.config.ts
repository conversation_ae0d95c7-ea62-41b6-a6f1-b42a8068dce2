import { ApplicationConfig } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { Configuration, provideApi } from '../generated-sources/openapi';

import { routes } from './app.routes';

// OpenAPI configuration
const openApiConfig = new Configuration({
  basePath: 'http://localhost:8083/JobUp',
});

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(),
    provideApi(openApiConfig)
  ]
};
